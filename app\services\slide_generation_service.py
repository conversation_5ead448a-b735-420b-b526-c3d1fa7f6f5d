"""
Slide Generation Service
Xử lý logic sinh nội dung slide từ lesson content và template structure sử dụng LLM
"""

import logging
import threading
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import TextbookRetrievalService
from app.services.google_slides_service import get_google_slides_service

logger = logging.getLogger(__name__)


class SlideGenerationService:
    """
    Service để sinh nội dung slide từ lesson content và template
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SlideGenerationService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.llm_service = None
        self.textbook_service = None
        self.slides_service = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure services are initialized"""
        if not self._service_initialized:
            logger.info("🔄 SlideGenerationService: First-time initialization triggered")
            self.llm_service = get_llm_service()
            self.textbook_service = TextbookRetrievalService()
            self.slides_service = get_google_slides_service()
            self._service_initialized = True
            logger.info("✅ SlideGenerationService: Initialization completed")

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return (self.llm_service and self.llm_service.is_available() and 
                self.slides_service and self.slides_service.is_available())

    async def generate_slides_from_lesson(
        self,
        lesson_id: str,
        template_id: str,
        config_prompt: Optional[str] = None,
        presentation_title: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ lesson_id và template_id (QUY TRÌNH MỚI)

        Args:
            lesson_id: ID của bài học
            template_id: ID của Google Slides template
            config_prompt: Prompt cấu hình tùy chỉnh (optional)
            presentation_title: Tiêu đề presentation tùy chỉnh (optional)

        Returns:
            Dict chứa kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Slide generation service not available"
            }

        try:
            logger.info(f"Starting NEW slide generation process for lesson {lesson_id} with template {template_id}")

            # Bước 1: Lấy nội dung bài học
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)
            if not lesson_result:
                return {
                    "success": False,
                    "error": f"Could not retrieve lesson content for {lesson_id}"
                }

            lesson_content = lesson_result.get("lesson_content", "")
            if not lesson_content:
                return {
                    "success": False,
                    "error": f"Empty lesson content for {lesson_id}"
                }

            # Bước 2: Copy template và phân tích cấu trúc của bản sao (QUY TRÌNH MỚI)
            new_title = presentation_title or f"Bài học {lesson_id} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            copy_and_analyze_result = await self.slides_service.copy_and_analyze_template(template_id, new_title)
            if not copy_and_analyze_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not copy and analyze template: {copy_and_analyze_result['error']}"
                }

            # Bước 3: Sinh nội dung slides bằng LLM với cấu trúc của bản sao
            slides_content = await self._generate_slides_content(
                lesson_content,
                copy_and_analyze_result,
                config_prompt
            )
            if not slides_content["success"]:
                return slides_content

            # Bước 4: Cập nhật nội dung vào bản sao đã tạo
            update_result = await self.slides_service.update_copied_presentation_content(
                copy_and_analyze_result["copied_presentation_id"],
                slides_content["slides"]
            )
            if not update_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not update presentation content: {update_result['error']}"
                }

            return {
                "success": True,
                "lesson_id": lesson_id,
                "original_template_id": template_id,
                "presentation_id": copy_and_analyze_result["copied_presentation_id"],
                "presentation_title": copy_and_analyze_result["presentation_title"],
                "web_view_link": copy_and_analyze_result["web_view_link"],
                "slides_created": update_result.get("slides_updated", 0) + update_result.get("slides_created", 0),
                "template_info": {
                    "title": copy_and_analyze_result["presentation_title"],
                    "layouts_count": copy_and_analyze_result["slide_count"]
                }
            }

        except Exception as e:
            logger.error(f"Error generating slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_slides_content(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Sinh nội dung slides bằng LLM (với cấu trúc từ bản sao)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy và phân tích
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa nội dung slides đã sinh
        """
        try:
            # Tạo prompt cho LLM với thông tin từ bản sao
            prompt = self._create_slide_generation_prompt(
                lesson_content,
                copied_presentation_info,
                config_prompt
            )

            # Validate and log prompt info for debugging
            if not self._validate_prompt(prompt, lesson_content, copied_presentation_info):
                return {
                    "success": False,
                    "error": "Invalid prompt or input data"
                }

            logger.info(f"Generated prompt length: {len(prompt)} characters")
            logger.debug(f"Prompt preview: {prompt[:500]}...")
            logger.info(f"Lesson content length: {len(lesson_content)} characters")
            logger.info(f"Presentation slides count: {len(copied_presentation_info.get('slides', []))}")

            # Gọi LLM để sinh nội dung với retry logic
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"LLM generation attempt {attempt + 1}/{max_retries}")

                llm_result = await self.llm_service._generate_content(prompt)

                if llm_result["success"] and llm_result.get("text") and llm_result["text"].strip():
                    logger.info(f"LLM generation successful on attempt {attempt + 1}")
                    break
                else:
                    logger.warning(f"LLM generation attempt {attempt + 1} failed: {llm_result.get('error', 'Empty response')}")
                    if attempt == max_retries - 1:
                        return {
                            "success": False,
                            "error": f"LLM generation failed after {max_retries} attempts: {llm_result.get('error', 'Empty response')}"
                        }

                    # Wait a bit before retry
                    import asyncio
                    await asyncio.sleep(1)

            # Parse kết quả từ LLM
            try:
                # Log raw LLM response for debugging
                logger.info(f"Raw LLM response length: {len(llm_result['text'])}")
                logger.debug(f"Raw LLM response: {llm_result['text'][:500]}...")

                # Check if response is empty
                if not llm_result["text"] or not llm_result["text"].strip():
                    logger.error("LLM returned empty response")
                    return {
                        "success": False,
                        "error": "LLM returned empty response"
                    }

                # Try to extract JSON from response (in case there's extra text)
                response_text = llm_result["text"].strip()

                # Look for JSON array in the response
                json_start = response_text.find('[')
                json_end = response_text.rfind(']') + 1

                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    logger.debug(f"Extracted JSON: {json_text[:200]}...")
                else:
                    # If no array found, try the whole response
                    json_text = response_text

                slides_data = json.loads(json_text)
                if not isinstance(slides_data, list):
                    raise ValueError("LLM output must be a list of slides")

                # Validate và format nội dung slides từ LLM
                validated_slides = self._validate_and_format_llm_slides(
                    slides_data,
                    copied_presentation_info
                )

                return {
                    "success": True,
                    "slides": validated_slides
                }

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM output as JSON: {e}")
                logger.error(f"Raw LLM response: {llm_result['text'][:1000] if llm_result['text'] else 'EMPTY'}")

                # Try fallback: create basic slide structure
                logger.info("Attempting fallback slide generation...")
                fallback_slides = self._create_fallback_slides(
                    lesson_content,
                    copied_presentation_info
                )

                if fallback_slides:
                    logger.info("Fallback slide generation successful")
                    return {
                        "success": True,
                        "slides": fallback_slides,
                        "fallback_used": True
                    }

                return {
                    "success": False,
                    "error": f"Invalid JSON from LLM and fallback failed: {e}"
                }

        except Exception as e:
            logger.error(f"Error generating slides content: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_slide_generation_prompt(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """
        Tạo prompt cho LLM để sinh nội dung slides (với thông tin từ bản sao)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            String prompt cho LLM
        """
        # Prompt cấu hình mặc định
        default_config = """
Bạn là chuyên gia thiết kế slide giáo dục. Hãy phân tích nội dung bài học và tạo slides đẹp, chuyên nghiệp.

NGUYÊN TẮC THIẾT KẾ:
1. MỖI SLIDE CHỈ CHỨA 1 Ý CHÍNH - không nhồi nhét quá nhiều thông tin
2. NỘI DUNG NGẮN GỌN - mỗi bullet point không quá 1-2 dòng
3. CÂN BẰNG THÔNG TIN - phân bổ đều nội dung giữa các slide
4. LOGIC TRÌNH BÀY - từ tổng quan đến chi tiết
5. DỄ ĐỌC - font chữ rõ ràng, không quá dài
6. TÍNH TOÁN KÍCH THƯỚC - nội dung phải vừa với kích thước element
7. TRÁNH VỠ LAYOUT - không để text dài quá khiến element bị tràn

YÊU CẦU CỤ THỂ:
- Tự động tạo thêm slides nếu nội dung nhiều (không bắt buộc đúng số slide template)
- Mỗi slide tối đa 3-4 bullet points
- Tiêu đề slide ngắn gọn, súc tích (tối đa 50 ký tự)
- Nội dung chi tiết chia đều giữa các slide
- Tránh text dài, ưu tiên bullet points
- Đảm bảo tính giáo dục và dễ hiểu cho học sinh
- QUAN TRỌNG: Tính toán độ dài nội dung phù hợp với kích thước element
"""

        # Sử dụng config_prompt nếu có, nếu không dùng mặc định
        final_config = config_prompt if config_prompt else default_config

        # Tạo thông tin chi tiết về slides và elements từ bản sao với thông tin layout
        slides_info = "CẤU TRÚC PRESENTATION HIỆN TẠI:\n"
        slides_info += f"Tổng số slides: {len(copied_presentation_info.get('slides', []))}\n\n"

        for i, slide in enumerate(copied_presentation_info.get("slides", [])):
            slides_info += f"SLIDE {i+1} (ID: {slide['slideId']}):\n"
            elements = slide.get("elements", [])

            if not elements:
                slides_info += "  - Không có elements có thể chỉnh sửa\n"
            else:
                slides_info += f"  - Có {len(elements)} elements có thể chỉnh sửa:\n"
                for j, element in enumerate(elements):
                    element_text = element.get('text', '').strip()
                    element_preview = element_text[:30] + "..." if len(element_text) > 30 else element_text

                    # Thêm thông tin về kích thước và style để LLM hiểu layout
                    size_info = self._get_element_size_info(element)
                    style_info = self._get_element_style_info(element)

                    slides_info += f"    {j+1}. {element['objectId']}: \"{element_preview}\"\n"
                    slides_info += f"       {size_info}\n"
                    slides_info += f"       {style_info}\n"
            slides_info += "\n"

        prompt = f"""
{final_config}

THÔNG TIN PRESENTATION ĐÃ COPY:
Tiêu đề: {copied_presentation_info.get('presentation_title', 'Không có tiêu đề')}
Số slide hiện tại: {copied_presentation_info.get('slide_count', 0)}
Presentation ID: {copied_presentation_info.get('copied_presentation_id')}

{slides_info}

NỘI DUNG BÀI HỌC:
{lesson_content}

HƯỚNG DẪN TẠO SLIDES:

1. PHÂN TÍCH NỘI DUNG: Chia nội dung bài học thành các phần logic
2. THIẾT KẾ SLIDES:
   - Slide 1: Tiêu đề chính + giới thiệu tổng quan
   - Slides tiếp theo: Từng phần nội dung chi tiết
   - Mỗi slide chỉ 1 chủ đề chính
   - Bullet points ngắn gọn (tối đa 3-4 points/slide)

3. TÍNH TOÁN KÍCH THƯỚC NỘI DUNG:
   - Dựa vào thông tin "Vừa phải: ~X ký tự" để tính toán độ dài nội dung
   - Nếu element nhỏ (< 100 ký tự): Nội dung ngắn gọn, 1-2 dòng
   - Nếu element trung bình (100-300 ký tự): 3-4 bullet points ngắn
   - Nếu element lớn (> 300 ký tự): Có thể viết chi tiết hơn
   - QUAN TRỌNG: Không vượt quá số ký tự ước tính để tránh vỡ layout

4. PHÂN LOẠI ELEMENT THEO STYLE:
   - "Tiêu đề chính/Tiêu đề giữa": Tiêu đề ngắn gọn, súc tích (< 50 ký tự)
   - "Tiêu đề phụ": Tiêu đề chi tiết hơn (< 80 ký tự)
   - "Nội dung chính": Bullet points hoặc đoạn văn chi tiết
   - Elements không có style đặc biệt: Nội dung linh hoạt theo kích thước

5. TẠO THÊM SLIDES NẾU CẦN:
   - Nếu nội dung nhiều, tạo thêm slides bằng cách duplicate slide cuối
   - Sử dụng slideId mới: "new_slide_1", "new_slide_2", etc.
   - Copy cấu trúc elements từ slide tương tự
   - Đảm bảo logic trình bày từ tổng quan đến chi tiết

YÊU CẦU OUTPUT:
QUAN TRỌNG: Chỉ trả về JSON array, không có text khác. Format:

[
  {{
    "slideId": "slide_id_from_presentation_or_new_slide_X",
    "action": "update",
    "updates": {{
      "element_object_id": "Nội dung ngắn gọn, rõ ràng phù hợp với kích thước",
      "another_element_id": "• Bullet point 1\\n• Bullet point 2\\n• Bullet point 3"
    }}
  }},
  {{
    "slideId": "new_slide_1",
    "action": "create",
    "baseSlideId": "slide_id_to_copy_from",
    "updates": {{
      "element_object_id": "Nội dung cho slide mới"
    }}
  }}
]

QUY TẮC QUAN TRỌNG:
- JSON array hợp lệ, bắt đầu [ kết thúc ]
- action: "update" (slide có sẵn) hoặc "create" (slide mới)
- Tính toán độ dài nội dung dựa trên thông tin kích thước element
- Tiêu đề: Ngắn gọn, súc tích (< 50 ký tự)
- Nội dung: Phù hợp với ước tính số ký tự của element
- Sử dụng \\n để xuống dòng trong bullet points
- Ưu tiên bullet points cho dễ đọc
- KHÔNG text giải thích ngoài JSON
- KHÔNG vượt quá giới hạn ký tự ước tính để tránh vỡ layout
"""

        return prompt

    def _validate_prompt(
        self,
        prompt: str,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any]
    ) -> bool:
        """
        Validate prompt and input data before sending to LLM

        Args:
            prompt: Generated prompt
            lesson_content: Lesson content
            copied_presentation_info: Presentation info

        Returns:
            bool: True if valid, False otherwise
        """
        try:
            # Check prompt length (not too short or too long)
            if len(prompt) < 100:
                logger.error("Prompt too short")
                return False

            if len(prompt) > 500000:  # Reasonable limit for most LLMs
                logger.error(f"Prompt too long: {len(prompt)} characters")
                return False

            # Check lesson content
            if not lesson_content or len(lesson_content.strip()) < 10:
                logger.error("Lesson content too short or empty")
                return False

            # Check presentation info
            if not copied_presentation_info:
                logger.error("No presentation info provided")
                return False

            slides = copied_presentation_info.get("slides", [])
            if not slides:
                logger.error("No slides found in presentation info")
                return False

            # Check if slides have editable elements
            total_elements = sum(len(slide.get("elements", [])) for slide in slides)
            if total_elements == 0:
                logger.error("No editable elements found in slides")
                return False

            logger.info(f"Prompt validation passed: {len(slides)} slides, {total_elements} elements")
            return True

        except Exception as e:
            logger.error(f"Error validating prompt: {e}")
            return False

    def _get_element_size_info(self, element: Dict[str, Any]) -> str:
        """
        Lấy thông tin kích thước element để LLM hiểu layout

        Args:
            element: Element data từ Google Slides API

        Returns:
            String mô tả kích thước element
        """
        try:
            # Lấy actualSize nếu có, nếu không thì dùng size gốc
            actual_size = element.get('actualSize')
            if actual_size:
                width = actual_size.get('width', {}).get('magnitude', 0)
                height = actual_size.get('height', {}).get('magnitude', 0)
                unit = actual_size.get('width', {}).get('unit', 'EMU')

                # Convert EMU to points for easier understanding (1 EMU = 1/12700 points)
                if unit == 'EMU':
                    width_pt = int(width / 12700) if width > 0 else 0
                    height_pt = int(height / 12700) if height > 0 else 0
                    return f"Kích thước: {width_pt}x{height_pt} points (Vừa phải: ~{self._estimate_text_capacity(width_pt, height_pt)} ký tự)"
                else:
                    return f"Kích thước: {width}x{height} {unit}"
            else:
                # Fallback to base size
                size = element.get('size', {})
                if size:
                    width = size.get('width', {}).get('magnitude', 0)
                    height = size.get('height', {}).get('magnitude', 0)
                    unit = size.get('width', {}).get('unit', 'EMU')

                    if unit == 'EMU' and width > 0:
                        width_pt = int(width / 12700)
                        height_pt = int(height / 12700)
                        return f"Kích thước: {width_pt}x{height_pt} points (Vừa phải: ~{self._estimate_text_capacity(width_pt, height_pt)} ký tự)"
                    else:
                        return f"Kích thước: {width}x{height} {unit}"

            return "Kích thước: Không xác định"
        except Exception as e:
            logger.warning(f"Error getting element size info: {e}")
            return "Kích thước: Không xác định"

    def _get_element_style_info(self, element: Dict[str, Any]) -> str:
        """
        Lấy thông tin style element để LLM hiểu format

        Args:
            element: Element data từ Google Slides API

        Returns:
            String mô tả style element
        """
        try:
            text_style = element.get('textStyle', {})
            if not text_style:
                return "Style: Mặc định"

            style_parts = []

            # Font size
            font_size = text_style.get('fontSize')
            if font_size and font_size.get('magnitude'):
                size_pt = font_size.get('magnitude')
                style_parts.append(f"Font: {size_pt}pt")

            # Font family
            font_family = text_style.get('fontFamily')
            if font_family:
                style_parts.append(f"Font: {font_family}")

            # Bold, italic, underline
            if text_style.get('bold'):
                style_parts.append("Đậm")
            if text_style.get('italic'):
                style_parts.append("Nghiêng")
            if text_style.get('underline'):
                style_parts.append("Gạch chân")

            # Placeholder type
            placeholder = text_style.get('placeholder')
            if placeholder and placeholder.get('type'):
                placeholder_type = placeholder.get('type')
                if placeholder_type == 'TITLE':
                    style_parts.append("Tiêu đề chính")
                elif placeholder_type == 'SUBTITLE':
                    style_parts.append("Tiêu đề phụ")
                elif placeholder_type == 'BODY':
                    style_parts.append("Nội dung chính")
                elif placeholder_type == 'CENTERED_TITLE':
                    style_parts.append("Tiêu đề giữa")

            return f"Style: {', '.join(style_parts)}" if style_parts else "Style: Mặc định"

        except Exception as e:
            logger.warning(f"Error getting element style info: {e}")
            return "Style: Không xác định"

    def _estimate_text_capacity(self, width_pt: int, height_pt: int) -> int:
        """
        Ước tính số ký tự có thể chứa trong element dựa trên kích thước

        Args:
            width_pt: Chiều rộng tính bằng points
            height_pt: Chiều cao tính bằng points

        Returns:
            Số ký tự ước tính
        """
        try:
            # Ước tính dựa trên font size trung bình 12pt
            # 1 ký tự ≈ 7 points width, 1 dòng ≈ 15 points height
            chars_per_line = max(1, int(width_pt / 7))
            lines_available = max(1, int(height_pt / 15))
            total_chars = chars_per_line * lines_available

            # Giảm 20% để tránh tràn
            return int(total_chars * 0.8)
        except:
            return 100  # Default fallback

    def _validate_and_format_llm_slides(
        self,
        slides_data: List[Dict[str, Any]],
        copied_presentation_info: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Validate và format nội dung slides từ LLM để đảm bảo tương thích với Google Slides API

        Args:
            slides_data: Raw slides data từ LLM
            copied_presentation_info: Thông tin presentation để validate

        Returns:
            List slides data đã được validate và format
        """
        try:
            validated_slides = []
            presentation_slides = copied_presentation_info.get('slides', [])

            # Tạo mapping của slide IDs và element IDs có sẵn
            available_slide_ids = {slide['slideId'] for slide in presentation_slides}
            available_element_ids = {}

            for slide in presentation_slides:
                slide_id = slide['slideId']
                available_element_ids[slide_id] = {
                    element['objectId']: element
                    for element in slide.get('elements', [])
                }

            logger.info(f"Validating {len(slides_data)} slides from LLM")
            logger.info(f"Available slide IDs: {list(available_slide_ids)}")

            for i, slide_data in enumerate(slides_data):
                try:
                    slide_id = slide_data.get('slideId')
                    action = slide_data.get('action', 'update')
                    updates = slide_data.get('updates', {})

                    if not slide_id:
                        logger.warning(f"Slide {i+1}: Missing slideId, skipping")
                        continue

                    if not updates:
                        logger.warning(f"Slide {i+1}: No updates provided, skipping")
                        continue

                    # Validate slide action
                    if action not in ['update', 'create']:
                        logger.warning(f"Slide {i+1}: Invalid action '{action}', defaulting to 'update'")
                        action = 'update'

                    # Validate slide ID cho action update
                    if action == 'update' and slide_id not in available_slide_ids:
                        logger.warning(f"Slide {i+1}: slideId '{slide_id}' not found in presentation, skipping")
                        continue

                    # Validate và format updates
                    formatted_updates = {}
                    slide_elements = available_element_ids.get(slide_id, {}) if action == 'update' else {}

                    for element_id, content in updates.items():
                        if not element_id or not content:
                            continue

                        # Validate element ID cho action update
                        if action == 'update' and element_id not in slide_elements:
                            logger.warning(f"Slide {i+1}: elementId '{element_id}' not found in slide '{slide_id}', skipping")
                            continue

                        # Format nội dung
                        formatted_content = self._format_slide_content(
                            content,
                            slide_elements.get(element_id) if action == 'update' else None
                        )

                        if formatted_content:
                            formatted_updates[element_id] = formatted_content

                    if not formatted_updates:
                        logger.warning(f"Slide {i+1}: No valid updates after formatting, skipping")
                        continue

                    # Tạo validated slide data
                    validated_slide = {
                        'slideId': slide_id,
                        'action': action,
                        'updates': formatted_updates
                    }

                    # Thêm baseSlideId cho action create
                    if action == 'create':
                        base_slide_id = slide_data.get('baseSlideId')
                        if base_slide_id and base_slide_id in available_slide_ids:
                            validated_slide['baseSlideId'] = base_slide_id
                        else:
                            # Fallback to first slide
                            if presentation_slides:
                                validated_slide['baseSlideId'] = presentation_slides[0]['slideId']
                                logger.info(f"Slide {i+1}: Using first slide as baseSlideId fallback")

                    validated_slides.append(validated_slide)
                    logger.info(f"Slide {i+1}: Validated successfully with {len(formatted_updates)} updates")

                except Exception as e:
                    logger.error(f"Error validating slide {i+1}: {e}")
                    continue

            logger.info(f"Validation completed: {len(validated_slides)}/{len(slides_data)} slides valid")
            return validated_slides

        except Exception as e:
            logger.error(f"Error in slide validation: {e}")
            return slides_data  # Return original data as fallback

    def _format_slide_content(self, content: str, element_info: Optional[Dict[str, Any]] = None) -> str:
        """
        Format nội dung slide để đảm bảo phù hợp với layout và không vỡ format

        Args:
            content: Nội dung gốc từ LLM
            element_info: Thông tin element để tính toán kích thước

        Returns:
            Nội dung đã được format
        """
        try:
            if not content or not isinstance(content, str):
                return ""

            # Clean content
            formatted_content = str(content).strip()

            # Ước tính giới hạn ký tự nếu có thông tin element
            char_limit = None
            if element_info:
                # Lấy thông tin kích thước
                actual_size = element_info.get('actualSize')
                if actual_size:
                    width = actual_size.get('width', {}).get('magnitude', 0)
                    height = actual_size.get('height', {}).get('magnitude', 0)
                    if width > 0 and height > 0:
                        width_pt = int(width / 12700)
                        height_pt = int(height / 12700)
                        char_limit = self._estimate_text_capacity(width_pt, height_pt)

            # Áp dụng giới hạn ký tự nếu có
            if char_limit and len(formatted_content) > char_limit:
                logger.info(f"Content too long ({len(formatted_content)} chars), truncating to {char_limit} chars")

                # Cắt ngắn thông minh - ưu tiên giữ nguyên bullet points
                if '•' in formatted_content or '\n' in formatted_content:
                    lines = formatted_content.split('\n')
                    truncated_lines = []
                    current_length = 0

                    for line in lines:
                        if current_length + len(line) + 1 <= char_limit:
                            truncated_lines.append(line)
                            current_length += len(line) + 1
                        else:
                            break

                    formatted_content = '\n'.join(truncated_lines)
                else:
                    # Cắt đơn giản
                    formatted_content = formatted_content[:char_limit-3] + "..."

            # Làm sạch format
            formatted_content = formatted_content.replace('\r\n', '\n').replace('\r', '\n')

            # Loại bỏ các dòng trống thừa
            lines = formatted_content.split('\n')
            cleaned_lines = []
            prev_empty = False

            for line in lines:
                line = line.strip()
                if line:
                    cleaned_lines.append(line)
                    prev_empty = False
                elif not prev_empty:
                    cleaned_lines.append('')
                    prev_empty = True

            # Loại bỏ dòng trống cuối
            while cleaned_lines and not cleaned_lines[-1]:
                cleaned_lines.pop()

            return '\n'.join(cleaned_lines)

        except Exception as e:
            logger.warning(f"Error formatting slide content: {e}")
            return str(content).strip() if content else ""

    def _create_fallback_slides(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Tạo slides cơ bản khi LLM fail (fallback mechanism)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy

        Returns:
            List các slide với nội dung cơ bản
        """
        try:
            fallback_slides = []
            slides_info = copied_presentation_info.get("slides", [])

            # Chia lesson content thành các phần
            content_parts = self._split_lesson_content(lesson_content)

            for i, slide_info in enumerate(slides_info):
                slide_id = slide_info.get("slideId")
                elements = slide_info.get("elements", [])

                if not elements:
                    continue

                updates = {}

                # Lấy nội dung tương ứng với slide
                content_part = content_parts[i] if i < len(content_parts) else content_parts[-1] if content_parts else lesson_content[:200]

                # Cập nhật từng element với nội dung cơ bản
                for j, element in enumerate(elements):
                    object_id = element.get("objectId")
                    if object_id:
                        if j == 0:  # Element đầu tiên thường là title
                            updates[object_id] = f"Slide {i+1}: {content_part[:50]}..."
                        else:  # Các element khác
                            updates[object_id] = content_part[:200] + "..." if len(content_part) > 200 else content_part

                if updates:
                    fallback_slides.append({
                        "slideId": slide_id,
                        "updates": updates
                    })

            return fallback_slides

        except Exception as e:
            logger.error(f"Error creating fallback slides: {e}")
            return []

    def _split_lesson_content(self, lesson_content: str) -> List[str]:
        """
        Chia lesson content thành các phần cho từng slide

        Args:
            lesson_content: Nội dung bài học

        Returns:
            List các phần nội dung
        """
        try:
            # Chia theo paragraph hoặc section
            parts = []

            # Thử chia theo dấu xuống dòng kép
            sections = lesson_content.split('\n\n')
            if len(sections) > 1:
                parts = [section.strip() for section in sections if section.strip()]
            else:
                # Chia theo dấu chấm
                sentences = lesson_content.split('. ')
                # Nhóm 2-3 câu thành một phần
                for i in range(0, len(sentences), 3):
                    part = '. '.join(sentences[i:i+3])
                    if part:
                        parts.append(part)

            # Đảm bảo có ít nhất 1 phần
            if not parts:
                parts = [lesson_content]

            return parts

        except Exception as e:
            logger.error(f"Error splitting lesson content: {e}")
            return [lesson_content]


# Hàm để lấy singleton instance
def get_slide_generation_service() -> SlideGenerationService:
    """
    Lấy singleton instance của SlideGenerationService
    Thread-safe lazy initialization

    Returns:
        SlideGenerationService: Singleton instance
    """
    return SlideGenerationService()
